"use client";

import { useCredit } from '@/context/CreditContext';
import { paymentService } from '@/services/paymentService';
import { useEffect, useState } from 'react';
import toast from 'react-hot-toast';
import { FaCoins, FaInfoCircle, FaShoppingCart, FaSync } from 'react-icons/fa';

export default function CreditBalance() {
  const { creditBalance, loading: creditLoading, refreshCreditBalance } = useCredit();
  const [purchasingPackageId, setPurchasingPackageId] = useState(null);
  const [selectedCurrency, setSelectedCurrency] = useState('USD');
  const [convertedPrices, setConvertedPrices] = useState({});
  const [loadingConversion, setLoadingConversion] = useState(false);
  const [customAmount, setCustomAmount] = useState('');

  const creditPackages = [
    {
      id: 1,
      name: 'Starter Pack',
      credits: 2000,
      price: 20,
      popular: false,
      description: 'Perfect for occasional use'
    },
    {
      id: 2,
      name: 'Professional Pack',
      credits: 5000,
      price: 50,
      popular: true,
      description: 'Great for regular users'
    },
    {
      id: 3,
      name: 'Enterprise Pack',
      credits: 10000,
      price: 100,
      popular: false,
      description: 'Best value for power users'
    }
  ];

  // Convert prices when currency changes
  useEffect(() => {
    const convertPrices = async () => {
      if (selectedCurrency === 'USD') {
        setConvertedPrices({});
        return;
      }

      setLoadingConversion(true);
      try {
        const prices = creditPackages.map(pkg => pkg.price);
        const conversionData = await paymentService.convertPrices(prices, selectedCurrency.toLowerCase());

        // Create a mapping of original price to converted price
        const priceMap = {};
        conversionData.prices.forEach((priceData, index) => {
          priceMap[prices[index]] = priceData.converted;
        });

        setConvertedPrices(priceMap);
      } catch (error) {
        console.error('Error converting prices:', error);
        toast.error('Failed to convert prices. Please try again.');
        setSelectedCurrency('USD'); // Fallback to USD
      } finally {
        setLoadingConversion(false);
      }
    };

    convertPrices();
  }, [selectedCurrency]);

  // Get display price for a package
  const getDisplayPrice = (originalPrice) => {
    if (selectedCurrency === 'USD') {
      return `$${originalPrice}`;
    }

    const convertedPrice = convertedPrices[originalPrice];
    if (convertedPrice !== undefined) {
      const currencySymbol = selectedCurrency === 'CAD' ? 'C$' : '$';
      return `${currencySymbol}${convertedPrice.toFixed(2)}`;
    }

    return loadingConversion ? '...' : `$${originalPrice}`;
  };

  // Handle credit purchase
  const handlePurchaseCredits = async (creditPackage) => {
    try {
      setPurchasingPackageId(creditPackage.id);

      // Use the payment service to initiate Stripe checkout with selected currency
      await paymentService.purchaseCredits(creditPackage, selectedCurrency.toLowerCase());

      // Note: User will be redirected to Stripe, so this code won't execute
      // The success handling will happen on the payment success page

    } catch (error) {
      console.error('Error purchasing credits:', error);
      toast.error(error.message || 'Failed to initiate payment. Please try again.');
    } finally {
      setPurchasingPackageId(null);
    }
  };

  return (
    <div className="space-y-6">
      {/* Current Balance */}
      <div className="bg-gradient-to-br from-yellow-50 to-orange-50 rounded-xl shadow-sm border border-yellow-200 p-6">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-xl font-semibold text-gray-800 flex items-center gap-2">
            <FaCoins className="text-yellow-600" />
            Current Balance
          </h3>
          <button
            onClick={refreshCreditBalance}
            disabled={creditLoading}
            className="flex items-center gap-2 px-3 py-1.5 text-sm bg-white border border-yellow-300 rounded-lg hover:bg-yellow-50 transition-colors disabled:opacity-50 text-gray-700"
          >
            <FaSync className={`${creditLoading ? 'animate-spin' : ''}`} />
            Refresh
          </button>
        </div>

        <div className="text-center">
          <div className="text-5xl font-bold text-yellow-700 mb-2">
            {creditBalance !== null ? creditBalance : '---'}
          </div>
          <p className="text-yellow-600 font-medium text-lg">Available Credits</p>
        </div>

        <div className="mt-6 bg-white/50 rounded-lg p-4">
          <div className="flex items-start gap-2">
            <FaInfoCircle className="text-blue-500 mt-0.5 flex-shrink-0" />
            <div className="text-sm text-gray-700">
              <p className="font-medium mb-1">How credits work:</p>
              <ul className="space-y-1 text-gray-600">
                <li>• Credits are used for AI-powered features across MizuFlow</li>
                <li>• Invoice automation and other AI features may have different costs</li>
                <li>• New users receive 500 free credits to get started</li>
                <li>• Credits never expire</li>
                <li>• Purchase more credits below when you need them</li>
              </ul>
            </div>
          </div>
        </div>
      </div>

      {/* Credit Packages */}
      <div>
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-xl font-semibold text-gray-800 flex items-center gap-2">
            <FaShoppingCart className="text-teal-600" />
            Purchase Credits
          </h3>

          {/* Currency Toggle */}
          <div className="flex items-center gap-2">
            <span className="text-sm text-black">Currency:</span>
            <div className="flex bg-gray-100 rounded-lg p-1">
              <button
                onClick={() => setSelectedCurrency('USD')}
                className={`px-3 py-1 text-sm font-medium rounded-md transition-all duration-200 ${
                  selectedCurrency === 'USD'
                    ? 'bg-white text-teal-600 shadow-sm'
                    : 'text-gray-600 hover:text-gray-800'
                }`}
              >
                USD
              </button>
              <button
                onClick={() => setSelectedCurrency('CAD')}
                className={`px-3 py-1 text-sm font-medium rounded-md transition-all duration-200 ${
                  selectedCurrency === 'CAD'
                    ? 'bg-white text-teal-600 shadow-sm'
                    : 'text-gray-600 hover:text-gray-800'
                }`}
              >
                CAD
              </button>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {creditPackages.map((pkg) => (
            <div
              key={pkg.id}
              className={`relative bg-white rounded-xl shadow-sm border-2 p-6 transition-all duration-300 group ${
                pkg.popular
                  ? 'border-teal-500 ring-2 ring-teal-100 hover:shadow-lg hover:ring-teal-200'
                  : 'border-gray-200 hover:border-teal-400 hover:shadow-lg hover:ring-2 hover:ring-teal-100'
              }`}
            >
              {pkg.popular && (
                <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                  <span className="bg-teal-500 text-white px-3 py-1 rounded-full text-sm font-medium">
                    Most Popular
                  </span>
                </div>
              )}

              <div className="text-center">
                <h4 className="text-lg font-semibold text-gray-800 mb-2 group-hover:text-teal-800 transition-colors duration-300">{pkg.name}</h4>
                <div className="text-3xl font-bold text-teal-600 mb-1 group-hover:text-teal-700 transition-colors duration-300">
                  {pkg.credits}
                </div>
                <p className="text-gray-600 text-sm mb-4 group-hover:text-gray-700 transition-colors duration-300">{pkg.description}</p>

                <div className="text-2xl font-bold text-gray-800 mb-4 group-hover:text-teal-800 transition-colors duration-300">
                  {getDisplayPrice(pkg.price)}
                </div>

                <button
                  className={`w-full py-2 px-4 rounded-lg font-medium transition-all duration-300 ${
                    pkg.popular
                      ? 'bg-teal-600 text-white hover:bg-teal-700 hover:shadow-md'
                      : 'bg-gray-100 text-gray-700 hover:bg-teal-600 hover:text-white hover:shadow-md group-hover:bg-teal-600 group-hover:text-white'
                  } ${(purchasingPackageId === pkg.id || loadingConversion) ? 'opacity-50 cursor-not-allowed' : ''}`}
                  onClick={() => handlePurchaseCredits(pkg)}
                  disabled={purchasingPackageId === pkg.id || loadingConversion}
                >
                  {purchasingPackageId === pkg.id ? (
                    <div className="flex items-center justify-center gap-2">
                      <div className="w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin"></div>
                      Processing...
                    </div>
                  ) : loadingConversion ? (
                    <div className="flex items-center justify-center gap-2">
                      <div className="w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin"></div>
                      Converting...
                    </div>
                  ) : (
                    'Purchase Credits'
                  )}
                </button>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Custom Amount Section */}
      <div className="mt-8 bg-white rounded-xl shadow-sm border-2 border-gray-200 p-6 hover:border-teal-400 hover:shadow-lg hover:ring-2 hover:ring-teal-100 transition-all duration-300">
        <h4 className="text-lg font-semibold text-gray-800 mb-4">Buy Credits</h4>
        <div className="flex flex-col space-y-4">
          <div className="flex items-center gap-4">
            <div className="flex-1">
              <label htmlFor="customAmount" className="block text-sm font-medium text-gray-700 mb-1">
                Enter Amount ({selectedCurrency})
              </label>
              <input
                type="number"
                id="customAmount"
                min={selectedCurrency === 'USD' ? 20 : 25} // Higher minimum for CAD
                step="0.01"
                placeholder={`Enter amount in ${selectedCurrency}`}
                className="w-full px-4 py-2 border border-gray-300 text-gray-700 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-teal-500"
                value={customAmount || ''}
                onChange={(e) => setCustomAmount(e.target.value)}
              />
            </div>
            <div className="flex-1">
              <p className="block text-sm font-medium text-gray-700 mb-1">Estimated Credits</p>
              <p className="text-2xl font-bold text-teal-600">
                {customAmount ? Math.floor(
                  selectedCurrency === 'USD' 
                    ? customAmount * 100 
                    : (customAmount / (convertedPrices[20] / 20)) * 100
                ) : '0'}
              </p>
            </div>
          </div>
          <button
            className={`w-full py-2 px-4 rounded-lg font-medium transition-all duration-300 
              ${customAmount >= (selectedCurrency === 'USD' ? 20 : 25)
                ? 'bg-teal-600 text-white hover:bg-teal-700 hover:shadow-md'
                : 'bg-gray-100 text-gray-400 cursor-not-allowed'
              }`}
            onClick={() => handlePurchaseCredits({
              id: 'custom',
              name: 'Custom Amount',
              price: parseFloat(customAmount),
              credits: Math.floor(
                selectedCurrency === 'USD' 
                  ? customAmount * 100 
                  : (customAmount / (convertedPrices[20] / 20)) * 100
              )
            })}
            disabled={!customAmount || customAmount < (selectedCurrency === 'USD' ? 20 : 25)}
          >
            Purchase Credits
          </button>
          <p className="text-sm text-gray-600 mt-2">
            Minimum amount: {selectedCurrency === 'USD' ? '$20 USD' : 'C$25'}
          </p>
        </div>
      </div>

      {/* Usage Tips */}
      <div className="bg-blue-50 rounded-xl border border-blue-200 p-6">
        <h3 className="text-lg font-semibold text-gray-800 mb-3 flex items-center gap-2">
          <FaInfoCircle className="text-blue-600" />
          Credit Usage Tips
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-gray-700">
          <div>
            <h4 className="font-medium mb-2">Maximize Your Credits:</h4>
            <ul className="space-y-1">
              <li>• Be specific in your questions to get better AI responses</li>
              <li>• Use appropriate features for your specific needs</li>
              <li>• Review previous results to avoid duplicate requests</li>
            </ul>
          </div>
          <div>
            <h4 className="font-medium mb-2">When to Buy More:</h4>
            <ul className="space-y-1">
              <li>• When you have less than credits required for a task</li>
              <li>• Before starting complex projects requiring AI assistance</li>
              <li>• For ongoing business automation needs</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
}
