import { API_ENDPOINTS } from "@/config/api";
import api from "@/services/api";
import { motion } from "framer-motion";
import { useEffect, useState } from "react";
import { toast } from "react-hot-toast";
import { TbArrowLeft, TbRefresh } from "react-icons/tb";

// Tax rates data for Canadian provinces
const CANADIAN_TAX_RATES = {
  "Alberta": { type: "GST", rate: "5%", description: "GST (5%)" },
  "British Columbia": { type: "GST+PST", rate: "12%", description: "GST (5%) + PST (7%)" },
  "Manitoba": { type: "GST+PST", rate: "12%", description: "GST (5%) + PST (7%)" },
  "New Brunswick": { type: "HST", rate: "15%", description: "HST (15%)" },
  "Newfoundland and Labrador": { type: "HST", rate: "15%", description: "HST (15%)" },
  "Northwest Territories": { type: "GST", rate: "5%", description: "GST (5%)" },
  "Nova Scotia": { type: "HST", rate: "14%", description: "HST (14%)" },
  "Nunavut": { type: "GST", rate: "5%", description: "GST (5%)" },
  "Ontario": { type: "HST", rate: "13%", description: "HST (13%)" },
  "Prince Edward Island": { type: "HST", rate: "15%", description: "HST (15%)" },
  "Quebec": { type: "GST+QST", rate: "14.975%", description: "GST (5%) + QST (9.975%)" },
  "Saskatchewan": { type: "GST+PST", rate: "11%", description: "GST (5%) + PST (6%)" },
  "Yukon": { type: "GST", rate: "5%", description: "GST (5%)" }
};

// List of Canadian provinces
const CANADIAN_PROVINCES = Object.keys(CANADIAN_TAX_RATES);

// List of US states
const US_STATES = [
  "Alabama", "Alaska", "Arizona", "Arkansas", "California", "Colorado", "Connecticut", 
  "Delaware", "Florida", "Georgia", "Hawaii", "Idaho", "Illinois", "Indiana", "Iowa", 
  "Kansas", "Kentucky", "Louisiana", "Maine", "Maryland", "Massachusetts", "Michigan", 
  "Minnesota", "Mississippi", "Missouri", "Montana", "Nebraska", "Nevada", "New Hampshire", 
  "New Jersey", "New Mexico", "New York", "North Carolina", "North Dakota", "Ohio", 
  "Oklahoma", "Oregon", "Pennsylvania", "Rhode Island", "South Carolina", "South Dakota", 
  "Tennessee", "Texas", "Utah", "Vermont", "Virginia", "Washington", "West Virginia", 
  "Wisconsin", "Wyoming"
];

const CompanyDetailsForm = ({ onNext, onBack, onReset, variants, selectedTemplate }) => {
  const [formData, setFormData] = useState({
    template_name: "",
    company_name: "",
    address_line_1: "",
    address_line_2: "",
    city: "",
    state_province: "",
    postal_code: "",
    country: "Canada", // Default to Canada
    phone: "",
    email: "",
    website: "",
    default_payment_terms: "Net 30 days",
    bank_name: "",
    account_number: "",
    routing_number: "",
    swift_code: "",
    business_registration: "",
    logo: null,
    logo_url: null,
    // New tax-related fields
    tax_type: "", // Will store the selected tax type (GST, HST, etc.)
    tax_rate: "", // Will store the actual tax rate
    tax_id: "", // For US tax ID
  });

  // Add state for tax-related UI control
  const [showTaxId, setShowTaxId] = useState(false);

  // Effect to handle tax rate updates based on province selection
  useEffect(() => {
    const province = formData.state_province;
    const country = formData.country;
    
    if (country === "Canada" && CANADIAN_TAX_RATES[province]) {
      const taxInfo = CANADIAN_TAX_RATES[province];
      setFormData(prev => ({
        ...prev,
        tax_type: taxInfo.type,
        tax_rate: taxInfo.rate
      }));
      setShowTaxId(false);
    } else if (country === "United States") {
      setFormData(prev => ({
        ...prev,
        tax_type: "US_TAX_ID",
        tax_rate: ""
      }));
      setShowTaxId(true);
    } else {
      setFormData(prev => ({
        ...prev,
        tax_type: "",
        tax_rate: ""
      }));
      setShowTaxId(false);
    }
  }, [formData.state_province, formData.country]);

  const [errors, setErrors] = useState({});
  const [loading, setLoading] = useState(false);
  const [logoUploading, setLogoUploading] = useState(false);
  const [showSaveDialog, setShowSaveDialog] = useState(false);
  const [saveTemplate, setSaveTemplate] = useState(false);
  const [tempFormData, setTempFormData] = useState(null);

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));

    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: ""
      }));
    }
  };

  const handleLogoUpload = async (e) => {
    const file = e.target.files[0];
    if (!file) return;

    // Validate file type
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
    if (!allowedTypes.includes(file.type)) {
      toast.error('Only image files (JPEG, PNG, GIF, WebP) are allowed');
      return;
    }

    // Validate file size (max 5MB)
    if (file.size > 5 * 1024 * 1024) {
      toast.error('File size must be less than 5MB');
      return;
    }

    setLogoUploading(true);

    try {
      const formData = new FormData();
      formData.append('logo', file);

      const response = await api.post(API_ENDPOINTS.COMPANY_LOGO_UPLOAD, formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });

      if (response.data.success) {
        setFormData(prev => ({
          ...prev,
          logo: file,
          logo_url: response.data.logo_url
        }));
        toast.success('Logo uploaded successfully!');
      }
    } catch (error) {
      console.error('Error uploading logo:', error);
      toast.error('Failed to upload logo. Please try again.');
    } finally {
      setLogoUploading(false);
    }
  };

  const removeLogo = () => {
    setFormData(prev => ({
      ...prev,
      logo: null,
      logo_url: null
    }));
  };

  const validateForm = () => {
    const newErrors = {};

    if (!formData.company_name.trim()) {
      newErrors.company_name = "Company name is required";
    }

    if (!formData.email.trim()) {
      newErrors.email = "Email is required";
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      newErrors.email = "Please enter a valid email address";
    }

    if (!formData.phone.trim()) {
      newErrors.phone = "Phone number is required";
    }

    if (!formData.address_line_1.trim()) {
      newErrors.address_line_1 = "Address is required";
    }

    if (!formData.city.trim()) {
      newErrors.city = "City is required";
    }

    if (!formData.state_province.trim()) {
      newErrors.state_province = "State/Province is required";
    }

    if (!formData.postal_code.trim()) {
      newErrors.postal_code = "Postal code is required";
    }

    if (!formData.country.trim()) {
      newErrors.country = "Country is required";
    }

    // Validate tax information
    if (formData.country === "United States" && !formData.tax_id.trim()) {
      newErrors.tax_id = "Tax ID is required for US companies";
    } else if (formData.country === "Canada" && !formData.tax_type) {
      newErrors.tax_type = "Tax type is required for Canadian companies";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    // Store form data temporarily and show save dialog
    setTempFormData(formData);
    setShowSaveDialog(true);
  };

  const handleSaveDialogResponse = async (shouldSave) => {
    if (shouldSave && !formData.template_name.trim()) {
      setErrors(prev => ({
        ...prev,
        template_name: "Template name is required when saving"
      }));
      return;
    }

    setShowSaveDialog(false);
    setSaveTemplate(shouldSave);
    setLoading(true);

    try {
      // Prepare common template data regardless of saving
      const templateData = {
        template_id: selectedTemplate.id,
        template_display_name: selectedTemplate.name,
        company_name: formData.company_name,
        address_line_1: formData.address_line_1,
        address_line_2: formData.address_line_2,
        city: formData.city,
        state_province: formData.state_province,
        postal_code: formData.postal_code,
        country: formData.country,
        phone: formData.phone,
        email: formData.email,
        website: formData.website,
        default_payment_terms: formData.default_payment_terms,
        bank_name: formData.bank_name,
        account_number: formData.account_number,
        routing_number: formData.routing_number,
        swift_code: formData.swift_code,
        tax_id: formData.tax_id,
        business_registration: formData.business_registration,
        logo_url: formData.logo_url
      };

      if (shouldSave) {
        // Add template name for saving
        templateData.template_name = formData.template_name;

        // Save company template to backend
        const response = await api.post(API_ENDPOINTS.COMPANY_TEMPLATES, templateData);

        if (response.data) {
          toast.success("Company template saved successfully!");
          // Pass the saved template data to the next step
          onNext({
            ...formData,
            savedTemplate: response.data
          });
        }
      } else {
        // Proceed without saving, but still pass the template data
        toast.success("Company information ready!");
        onNext({
          ...formData,
          // Create a temporary template object with the current form data
          savedTemplate: {
            ...templateData,
            id: `temp_${Date.now()}`, // Generate a temporary ID
            template_name: "Temporary Template"
          }
        });
      }
    } catch (error) {
      console.error("Error handling template:", error);

      if (error.response?.data?.template_name) {
        setErrors(prev => ({
          ...prev,
          template_name: error.response.data.template_name[0]
        }));
      } else {
        toast.error("Failed to process company details. Please try again.");
      }
    } finally {
      setLoading(false);
    }
  };

  return (
    <motion.div
      className="flex flex-col gap-6"
      variants={variants}
    >
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <motion.button
            onClick={onBack}
            className="w-12 h-12 rounded-full flex items-center justify-center bg-blue-600 text-white shadow-md hover:bg-blue-700 transition-colors"
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            aria-label="Go back"
          >
            <TbArrowLeft className="h-6 w-6" />
          </motion.button>
          <div>
            <h2 className="text-xl font-medium text-gray-700">
              Company Details
            </h2>
            <p className="text-gray-600 mt-1">
              Fill in your company information for the {selectedTemplate?.name} template
            </p>
          </div>
        </div>
        <motion.button
          onClick={onReset}
          className="w-12 h-12 rounded-full flex items-center justify-center bg-red-100 text-red-600 hover:bg-red-200 shadow-md transition-colors"
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
          aria-label="Reset process"
        >
          <TbRefresh className="h-6 w-6" />
        </motion.button>
      </div>

      {/* Form */}
      <form onSubmit={handleSubmit} className="space-y-6">

        {/* Logo Upload */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Company Logo
          </label>
          <div className="flex items-center space-x-4">
            {formData.logo_url ? (
              <div className="flex items-center space-x-4">
                <img
                  src={formData.logo_url}
                  alt="Company Logo"
                  className="w-16 h-16 object-contain border border-gray-300 rounded"
                />
                <button
                  type="button"
                  onClick={removeLogo}
                  className="px-3 py-1 text-sm text-red-600 hover:text-red-800 border border-red-300 rounded hover:border-red-500 transition-colors"
                >
                  Remove
                </button>
              </div>
            ) : (
              <div className="flex items-center space-x-4">
                <label className="cursor-pointer">
                  <input
                    type="file"
                    accept="image/*"
                    onChange={handleLogoUpload}
                    className="hidden"
                    disabled={logoUploading}
                  />
                  <div className={`px-4 py-2 border border-gray-300 rounded-md text-sm font-medium transition-colors ${
                    logoUploading
                      ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                      : 'bg-white text-gray-700 hover:bg-gray-50 cursor-pointer'
                  }`}>
                    {logoUploading ? 'Uploading...' : 'Choose Logo'}
                  </div>
                </label>
                <span className="text-sm text-gray-500">
                  PNG, JPG, GIF up to 5MB
                </span>
              </div>
            )}
          </div>
        </div>

        {/* Company Information */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Company Name *
            </label>
            <input
              type="text"
              name="company_name"
              value={formData.company_name}
              onChange={handleInputChange}
              className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-gray-700 ${
                errors.company_name ? 'border-red-500' : 'border-gray-300'
              }`}
              placeholder="Enter company name"
            />
            {errors.company_name && (
              <p className="text-red-600 text-xs mt-1">{errors.company_name}</p>
            )}
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Email *
            </label>
            <input
              type="email"
              name="email"
              value={formData.email}
              onChange={handleInputChange}
              className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-gray-700 ${
                errors.email ? 'border-red-500' : 'border-gray-300'
              }`}
              placeholder="<EMAIL>"
            />
            {errors.email && (
              <p className="text-red-600 text-xs mt-1">{errors.email}</p>
            )}
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Phone *
            </label>
            <input
              type="tel"
              name="phone"
              value={formData.phone}
              onChange={handleInputChange}
              className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-gray-700 ${
                errors.phone ? 'border-red-500' : 'border-gray-300'
              }`}
              placeholder="+****************"
            />
            {errors.phone && (
              <p className="text-red-600 text-xs mt-1">{errors.phone}</p>
            )}
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Website
            </label>
            <input
              type="url"
              name="website"
              value={formData.website}
              onChange={handleInputChange}
              className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-gray-700"
              placeholder="https://www.company.com"
            />
          </div>

        </div>

        {/* Address Information */}
        <div className="space-y-4">
          <h3 className="text-lg font-medium text-gray-700">Address Information</h3>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Address Line 1 *
            </label>
            <input
              type="text"
              name="address_line_1"
              value={formData.address_line_1}
              onChange={handleInputChange}
              className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-gray-700 ${
                errors.address_line_1 ? 'border-red-500' : 'border-gray-300'
              }`}
              placeholder="123 Business Street"
            />
            {errors.address_line_1 && (
              <p className="text-red-600 text-xs mt-1">{errors.address_line_1}</p>
            )}
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Address Line 2
            </label>
            <input
              type="text"
              name="address_line_2"
              value={formData.address_line_2}
              onChange={handleInputChange}
              className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-gray-700"
              placeholder="Suite, apartment, etc. (optional)"
            />
          </div>

          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                City *
              </label>
              <input
                type="text"
                name="city"
                value={formData.city}
                onChange={handleInputChange}
                className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-gray-700 ${
                  errors.city ? 'border-red-500' : 'border-gray-300'
                }`}
                placeholder="New York"
              />
              {errors.city && (
                <p className="text-red-600 text-xs mt-1">{errors.city}</p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                State/Province *
              </label>
              <select
                name="state_province"
                value={formData.state_province}
                onChange={handleInputChange}
                className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-gray-700 ${
                  errors.state_province ? 'border-red-500' : 'border-gray-300'
                }`}
              >
                <option value="">Select State/Province</option>
                {formData.country === "Canada" ? (
                  CANADIAN_PROVINCES.map(province => (
                    <option key={province} value={province}>{province}</option>
                  ))
                ) : (
                  US_STATES.map(state => (
                    <option key={state} value={state}>{state}</option>
                  ))
                )}
              </select>
              {errors.state_province && (
                <p className="text-red-600 text-xs mt-1">{errors.state_province}</p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Postal Code *
              </label>
              <input
                type="text"
                name="postal_code"
                value={formData.postal_code}
                onChange={handleInputChange}
                className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-gray-700 ${
                  errors.postal_code ? 'border-red-500' : 'border-gray-300'
                }`}
                placeholder="12345"
              />
              {errors.postal_code && (
                <p className="text-red-600 text-xs mt-1">{errors.postal_code}</p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Country *
              </label>
              <select
                name="country"
                value={formData.country}
                onChange={handleInputChange}
                className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-gray-700 ${
                  errors.country ? 'border-red-500' : 'border-gray-300'
                }`}
              >
                <option value="Canada">Canada</option>
                <option value="United States">United States</option>
              </select>
              {errors.country && (
                <p className="text-red-600 text-xs mt-1">{errors.country}</p>
              )}
            </div>
          </div>
        </div>

        {/* Tax Details Section */}
        <div className="space-y-4">
          <h3 className="text-lg font-medium text-gray-700">Tax Details</h3>
          <p className="text-sm text-gray-600">
            {formData.country === "Canada" 
              ? "Select the applicable tax rate for your province" 
              : "Enter your tax identification number"}
          </p>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {formData.country === "Canada" ? (
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Tax Rate *
                </label>
                <div className="relative">
                  <select
                    name="tax_type"
                    value={formData.tax_type}
                    onChange={handleInputChange}
                    className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-gray-700 ${
                      errors.tax_type ? 'border-red-500' : 'border-gray-300'
                    }`}
                    disabled={!formData.state_province}
                  >
                    <option value="">Select Tax Rate</option>
                    {formData.state_province && CANADIAN_TAX_RATES[formData.state_province] && (
                      <option value={CANADIAN_TAX_RATES[formData.state_province].type}>
                        {CANADIAN_TAX_RATES[formData.state_province].description}
                      </option>
                    )}
                  </select>
                  {formData.tax_rate && (
                    <span className="absolute right-10 top-2 text-gray-500">
                      ({formData.tax_rate})
                    </span>
                  )}
                </div>
                {errors.tax_type && (
                  <p className="text-red-600 text-xs mt-1">{errors.tax_type}</p>
                )}
              </div>
            ) : (
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Tax ID *
                </label>
                <input
                  type="text"
                  name="tax_id"
                  value={formData.tax_id}
                  onChange={handleInputChange}
                  className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-gray-700 ${
                    errors.tax_id ? 'border-red-500' : 'border-gray-300'
                  }`}
                  placeholder="Enter your tax ID"
                />
                {errors.tax_id && (
                  <p className="text-red-600 text-xs mt-1">{errors.tax_id}</p>
                )}
              </div>
            )}
          </div>
        </div>

        {/* Banking Information */}
        <div className="space-y-4">
          <h3 className="text-lg font-medium text-gray-700">Banking Information</h3>
          <p className="text-sm text-gray-600">Optional banking details for invoices</p>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Bank Name
              </label>
              <input
                type="text"
                name="bank_name"
                value={formData.bank_name}
                onChange={handleInputChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-gray-700"
                placeholder="Enter bank name"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Account Number
              </label>
              <input
                type="text"
                name="account_number"
                value={formData.account_number}
                onChange={handleInputChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-gray-700"
                placeholder="Enter account number"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Routing Number
              </label>
              <input
                type="text"
                name="routing_number"
                value={formData.routing_number}
                onChange={handleInputChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-gray-700"
                placeholder="Enter routing number"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                SWIFT Code
              </label>
              <input
                type="text"
                name="swift_code"
                value={formData.swift_code}
                onChange={handleInputChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-gray-700"
                placeholder="Enter SWIFT code"
              />
            </div>
          </div>
        </div>

        {/* Business Information */}
        <div className="space-y-4">
          <h3 className="text-lg font-medium text-gray-700">Business Information</h3>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Default Payment Terms
              </label>
              <select
                name="default_payment_terms"
                value={formData.default_payment_terms}
                onChange={handleInputChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-gray-700"
              >
                <option value="Net 30 days">Net 30 days</option>
                <option value="Net 15 days">Net 15 days</option>
                <option value="Net 7 days">Net 7 days</option>
                <option value="Due on receipt">Due on receipt</option>
                <option value="Cash on delivery">Cash on delivery</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Business Registration
              </label>
              <input
                type="text"
                name="business_registration"
                value={formData.business_registration}
                onChange={handleInputChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-gray-700"
                placeholder="Enter business registration number"
              />
            </div>
          </div>
        </div>

        {/* Submit Button */}
        <div className="flex justify-end">
          <button
            type="submit"
            disabled={loading}
            className={`px-6 py-2 font-medium rounded-lg transition-colors ${
              loading
                ? 'bg-gray-400 text-gray-200 cursor-not-allowed'
                : 'bg-blue-600 text-white hover:bg-blue-700'
            }`}
          >
            {loading ? (
              <div className="flex items-center">
                <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                Saving...
              </div>
            ) : (
              "Save & Continue"
            )}
          </button>
        </div>
      </form>

      {/* Save Template Dialog */}
      {showSaveDialog && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg p-6 max-w-md w-full shadow-xl">
            <h3 className="text-xl font-semibold text-gray-800 mb-4">Save Template</h3>
            <p className="text-gray-600 mb-4">
              Would you like to save this company information as a template for future use?
            </p>

            {saveTemplate && (
              <div className="mb-4">
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Template Name *
                </label>
                <input
                  type="text"
                  name="template_name"
                  value={formData.template_name}
                  onChange={handleInputChange}
                  className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-gray-700 ${
                    errors.template_name ? 'border-red-500' : 'border-gray-300'
                  }`}
                  placeholder="e.g., My Company Template"
                />
                {errors.template_name && (
                  <p className="text-red-600 text-xs mt-1">{errors.template_name}</p>
                )}
              </div>
            )}

            <div className="flex justify-end gap-3">
              <button
                className="px-4 py-2 bg-gray-200 text-gray-800 rounded-lg hover:bg-gray-300 transition-colors"
                onClick={() => handleSaveDialogResponse(false)}
                disabled={loading}
              >
                No, Continue Without Saving
              </button>
              <button
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                onClick={() => {
                  if (!saveTemplate) {
                    setSaveTemplate(true);
                  } else {
                    handleSaveDialogResponse(true);
                  }
                }}
                disabled={loading}
              >
                {loading ? (
                  <div className="flex items-center">
                    <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 714 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    Saving...
                  </div>
                ) : saveTemplate ? (
                  "Save Template"
                ) : (
                  "Yes, Save as Template"
                )}
              </button>
            </div>
          </div>
        </div>
      )}
    </motion.div>
  );
};

export default CompanyDetailsForm;
