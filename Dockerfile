# Use Python 3.11 slim image
FROM python:3.11-slim

# Set environment variables
ENV PYTHONDONTWRITEBYTECODE=1
ENV PYTHONUNBUFFERED=1

# Set work directory
WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \
    build-essential \
    pkg-config \
    default-libmysqlclient-dev \
    libpango-1.0-0 \
    libharfbuzz0b \
    libpangoft2-1.0-0 \
    libharfbuzz-subset0 \
    && rm -rf /var/lib/apt/lists/*

# Install Python dependencies
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt
RUN pip install --no-cache-dir gunicorn mysqlclient

# Copy project
COPY . .

# Make sure media directory exists
RUN mkdir -p media

# Make sure static directory exists and has correct permissions
RUN mkdir -p staticfiles
RUN chmod -R 755 staticfiles
RUN chmod -R 755 media

# Run gunicorn
CMD ["gunicorn", "--config", "gunicorn_config.py", "config.wsgi:application"] 